[package]
name = "tray-icon"
version = "0.20.1"
edition = "2021"
description = "Create tray icons for desktop applications"
homepage = "https://github.com/tauri-apps/tray-icon"
repository = "https://github.com/tauri-apps/tray-icon"
license = "MIT OR Apache-2.0"
categories = ["gui"]
rust-version = "1.71"

[features]
default = ["libxdo"]
libxdo = ["muda/libxdo"]
serde = ["muda/serde", "dep:serde"]
common-controls-v6 = ["muda/common-controls-v6"]

[dependencies]
muda = { version = "0.16", default-features = false }
crossbeam-channel = "0.5"
once_cell = "1"
thiserror = "2.0"
serde = { version = "1", optional = true }

[target."cfg(target_os = \"windows\")".dependencies.windows-sys]
version = "0.59"
features = [
  "Win32_UI_WindowsAndMessaging",
  "Win32_Foundation",
  "Win32_System_SystemServices",
  "Win32_Graphics_Gdi",
  "Win32_UI_Shell",
]

[target."cfg(target_os = \"linux\")".dependencies]
libappindicator = "0.9"
dirs = "6"

[target."cfg(target_os = \"linux\")".dev-dependencies]
gtk = "0.18"

[target."cfg(target_os = \"macos\")".dependencies]
objc2 = "0.6.0"
objc2-core-graphics = { version = "0.3.0", default-features = false, features = [
  "std",
  "CGDirectDisplay",
] }
objc2-core-foundation = { version = "0.3.0", default-features = false, features = [
  "std",
  "CFCGTypes",
  "CFRunLoop",
] }
objc2-foundation = { version = "0.3.0", default-features = false, features = [
  "std",
  "block2",
  "objc2-core-foundation",
  "NSArray",
  "NSData",
  "NSEnumerator",
  "NSGeometry",
  "NSString",
  "NSThread",
] }
objc2-app-kit = { version = "0.3.0", default-features = false, features = [
  "std",
  "objc2-core-foundation",
  "NSButton",
  "NSCell",
  "NSControl",
  "NSEvent",
  "NSImage",
  "NSMenu",
  "NSResponder",
  "NSStatusBar",
  "NSStatusBarButton",
  "NSStatusItem",
  "NSTrackingArea",
  "NSView",
  "NSWindow",
] }

[target."cfg(any(target_os = \"linux\", target_os = \"macos\"))".dependencies]
png = "0.17"

[dev-dependencies]
winit = "0.30"
tao = "0.33"
image = "0.25"
eframe = "0.31"
serde_json = "1"
