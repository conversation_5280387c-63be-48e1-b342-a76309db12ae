// Copyright 2022-2022 Tauri Programme within The Commons Conservancy
// SPDX-License-Identifier: Apache-2.0
// SPDX-License-Identifier: MIT

#![allow(unused)]

use tao::{
    event::Event,
    event_loop::{ControlFlow, EventLoopBuilder},
};
use tray_icon::{
    menu::{AboutMetadata, Menu, MenuEvent, MenuItem, PredefinedMenuItem, Submenu},
    TrayIconBuilder, TrayIconEvent,
};

enum UserEvent {
    TrayIconEvent(tray_icon::TrayIconEvent),
    MenuEvent(tray_icon::menu::MenuEvent),
}

fn main() {
    println!("🔧 macOS 子菜单修复测试");
    println!("========================");
    println!("请查看系统托盘图标（通常在屏幕右上角），右键点击查看菜单");
    println!("");
    println!("测试步骤:");
    println!("1. 右键点击托盘图标");
    println!("2. 查看菜单中是否能看到 '📋 测试子菜单' 选项");
    println!("3. 将鼠标悬停在 '📋 测试子菜单' 上");
    println!("4. 检查是否出现子菜单，包含 '子项目 1' 和 '子项目 2'");
    println!("5. 根据实际情况点击相应的反馈按钮:");
    println!("   - ✅ 如果子菜单正常显示");
    println!("   - ❌ 如果子菜单消失、不显示或有其他问题");
    println!("6. 点击 '🚪 退出测试' 结束程序");
    println!("");
    println!("注意: 如果第一次右键没有看到子菜单，请尝试:");
    println!("- 再次右键点击托盘图标");
    println!("- 等待几秒钟后再试");
    println!("- 移动鼠标到其他地方再回来");
    println!("========================");

    let path = concat!(env!("CARGO_MANIFEST_DIR"), "/examples/icon.png");

    let event_loop = EventLoopBuilder::<UserEvent>::with_user_event().build();

    // set a tray event handler that forwards the event and wakes up the event loop
    let proxy = event_loop.create_proxy();
    TrayIconEvent::set_event_handler(Some(move |event| {
        proxy.send_event(UserEvent::TrayIconEvent(event));
    }));

    // set a menu event handler that forwards the event and wakes up the event loop
    let proxy = event_loop.create_proxy();
    MenuEvent::set_event_handler(Some(move |event| {
        proxy.send_event(UserEvent::MenuEvent(event));
    }));

 // 创建最简单的菜单
    let menu = Menu::new();

    // 添加一个子菜单
    let submenu = Submenu::new("📋 测试子菜单", true);

    // 在子菜单中添加项目
    let sub_item1 = MenuItem::new("子项目 1", true, None);
    submenu.append(&sub_item1).unwrap();

    let sub_item2 = MenuItem::new("子项目 2", true, None);
    submenu.append(&sub_item2).unwrap();

    // 将子菜单添加到主菜单
    menu.append(&submenu).unwrap();

    // 添加分隔线
    menu.append(&PredefinedMenuItem::separator()).unwrap();

    // 添加两个普通菜单项
    let normal_item1 = MenuItem::new("🔧 普通菜单 1", true, None);
    menu.append(&normal_item1).unwrap();

    let normal_item2 = MenuItem::new("🔧 普通菜单 2", true, None);
    menu.append(&normal_item2).unwrap();

    // 添加分隔线
    menu.append(&PredefinedMenuItem::separator()).unwrap();

    // 添加分隔线
    menu.append(&PredefinedMenuItem::separator()).unwrap();

    // 添加反馈按钮
    let feedback_good = MenuItem::new("✅ 子菜单显示正常", true, None);
    menu.append(&feedback_good).unwrap();

    let feedback_bad = MenuItem::new("❌ 子菜单有问题/消失", true, None);
    menu.append(&feedback_bad).unwrap();

    // 添加分隔线
    menu.append(&PredefinedMenuItem::separator()).unwrap();

    // 添加退出
    let quit = MenuItem::new("🚪 退出测试", true, None);
    menu.append(&quit).unwrap();

    // 打印菜单结构调试信息
    println!("");
    println!("📋 菜单结构调试信息:");
    println!("- 子菜单 ID: {:?}", submenu.id());
    println!("- 子项目1 ID: {:?}", sub_item1.id());
    println!("- 子项目2 ID: {:?}", sub_item2.id());
    println!("- 普通菜单1 ID: {:?}", normal_item1.id());
    println!("- 普通菜单2 ID: {:?}", normal_item2.id());
    println!("- 反馈好 ID: {:?}", feedback_good.id());
    println!("- 反馈坏 ID: {:?}", feedback_bad.id());
    println!("- 退出 ID: {:?}", quit.id());
    println!("");



    let mut tray_icon = None;

    let menu_channel = MenuEvent::receiver();
    let tray_channel = TrayIconEvent::receiver();

    event_loop.run(move |event, _, control_flow| {
        *control_flow = ControlFlow::Wait;

        match event {
            Event::NewEvents(tao::event::StartCause::Init) => {

                // We create the icon once the event loop is actually running
                // to prevent issues like https://github.com/tauri-apps/tray-icon/issues/90
                tray_icon = Some(
                    TrayIconBuilder::new()
                        .with_menu(Box::new(menu.clone()))
                        .with_tooltip("tao - awesome windowing lib")
                        .with_title("xxxxx")
                        .build()
                        .unwrap(),
                );

                // We have to request a redraw here to have the icon actually show up.
                // Tao only exposes a redraw method on the Window so we use core-foundation directly.
                #[cfg(target_os = "macos")]
                unsafe {
                    use objc2_core_foundation::{CFRunLoopGetMain, CFRunLoopWakeUp};

                    let rl = CFRunLoopGetMain().unwrap();
                    CFRunLoopWakeUp(&rl);
                }
            }

            Event::UserEvent(UserEvent::TrayIconEvent(event)) => {
                println!("{event:?}");
            }

            Event::UserEvent(UserEvent::MenuEvent(event)) => {
                println!("菜单事件: {:?}", event);

                if event.id == quit.id() {
                    println!("用户选择退出");
                    tray_icon.take();
                    *control_flow = ControlFlow::Exit;
                } else if event.id == feedback_good.id() {
                    println!("🎉 用户反馈: 子菜单显示正常！");
                    println!("感谢您的反馈，修复成功！");
                    println!("这意味着 macOS 子菜单问题已经解决。");
                } else if event.id == feedback_bad.id() {
                    println!("⚠️  用户反馈: 子菜单有问题或消失");
                    println!("请报告此问题，我们需要进一步修复");
                    println!("问题描述: 子菜单在第一次显示时可能不可见或消失");
                    println!("这表明我们的修复还不完整，需要更深入的调查");
                } else if event.id == sub_item1.id() {
                    println!("用户点击了: 子项目 1");
                } else if event.id == sub_item2.id() {
                    println!("用户点击了: 子项目 2");
                } else if event.id == normal_item1.id() {
                    println!("用户点击了: 普通菜单 1");
                } else if event.id == normal_item2.id() {
                    println!("用户点击了: 普通菜单 2");
                }
            }

            _ => {}
        }
    })
}

