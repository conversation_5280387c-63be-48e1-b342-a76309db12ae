// Copyright 2022-2022 Tauri Programme within The Commons Conservancy
// SPDX-License-Identifier: Apache-2.0
// SPDX-License-Identifier: MIT

#![allow(unused)]

use tao::{
    event::Event,
    event_loop::{ControlFlow, EventLoopBuilder},
};
use tray_icon::{
    menu::{Menu, MenuEvent, MenuItem, PredefinedMenuItem, Submenu},
    TrayIconBuilder, TrayIconEvent,
};

enum UserEvent {
    TrayIconEvent(tray_icon::TrayIconEvent),
    MenuEvent(tray_icon::menu::MenuEvent),
}

fn main() {
    let path = concat!(env!("CARGO_MANIFEST_DIR"), "/examples/icon.png");

    let event_loop = EventLoopBuilder::<UserEvent>::with_user_event().build();

    // set a tray event handler that forwards the event and wakes up the event loop
    let proxy = event_loop.create_proxy();
    TrayIconEvent::set_event_handler(Some(move |event| {
        proxy.send_event(UserEvent::TrayIconEvent(event));
    }));

    // set a menu event handler that forwards the event and wakes up the event loop
    let proxy = event_loop.create_proxy();
    MenuEvent::set_event_handler(Some(move |event| {
        proxy.send_event(UserEvent::MenuEvent(event));
    }));

    // 创建测试菜单
    let menu = Menu::new();

    // 添加一个子菜单
    let submenu = Submenu::new("📋 测试子菜单", true);

    // 在子菜单中添加项目
    let sub_item1 = MenuItem::new("子项目 1", true, None);
    submenu.append(&sub_item1).unwrap();

    let sub_item2 = MenuItem::new("子项目 2", true, None);
    submenu.append(&sub_item2).unwrap();

    // 将子菜单添加到主菜单
    menu.append(&submenu).unwrap();

    // 添加分隔线
    menu.append(&PredefinedMenuItem::separator()).unwrap();

    // 添加普通菜单项
    let normal_item = MenuItem::new("🔧 普通菜单", true, None);
    menu.append(&normal_item).unwrap();

    // 添加退出
    let quit = MenuItem::new("🚪 退出", true, None);
    menu.append(&quit).unwrap();

    let mut tray_icon = None;

    event_loop.run(move |event, _, control_flow| {
        *control_flow = ControlFlow::Wait;

        match event {
            Event::NewEvents(tao::event::StartCause::Init) => {
                // We create the icon once the event loop is actually running
                // to prevent issues like https://github.com/tauri-apps/tray-icon/issues/90
                tray_icon = Some(
                    TrayIconBuilder::new()
                        .with_menu(Box::new(menu.clone()))
                        .with_tooltip("测试子菜单")
                        .with_title("测试")
                        .build()
                        .unwrap(),
                );

                // We have to request a redraw here to have the icon actually show up.
                // Tao only exposes a redraw method on the Window so we use core-foundation directly.
                #[cfg(target_os = "macos")]
                unsafe {
                    use objc2_core_foundation::{CFRunLoopGetMain, CFRunLoopWakeUp};

                    let rl = CFRunLoopGetMain().unwrap();
                    CFRunLoopWakeUp(&rl);
                }
            }

            Event::UserEvent(UserEvent::TrayIconEvent(event)) => {
                println!("Tray event: {:?}", event);
            }

            Event::UserEvent(UserEvent::MenuEvent(event)) => {
                println!("Menu event: {:?}", event);

                if event.id == quit.id() {
                    tray_icon.take();
                    *control_flow = ControlFlow::Exit;
                }
            }

            _ => {}
        }
    })
}
