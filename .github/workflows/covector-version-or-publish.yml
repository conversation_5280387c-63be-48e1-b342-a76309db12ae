# Copyright 2022-2022 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT

name: covector version or publish

on:
  push:
    branches:
      - dev

jobs:
  version-or-publish:
    runs-on: ubuntu-latest
    timeout-minutes: 65
    outputs:
      change: ${{ steps.covector.outputs.change }}
      commandRan: ${{ steps.covector.outputs.commandRan }}
      successfulPublish: ${{ steps.covector.outputs.successfulPublish }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: cargo login
        run: cargo login ${{ secrets.ORG_CRATES_IO_TOKEN  }}
        
      - name: git config
        run: |
          git config --global user.name "${{ github.event.pusher.name }}"
          git config --global user.email "${{ github.event.pusher.email }}"

      - name: covector version or publish (publish when no change files present)
        uses: jbolda/covector/packages/action@covector-v0
        id: covector
        env:
          NODE_AUTH_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          command: 'version-or-publish'
          createRelease: true
          recognizeContributors: true

      - name: Sync Cargo.lock
        if: steps.covector.outputs.commandRan == 'version'
        run: cargo tree --depth 0

      - name: Create Pull Request With Versions Bumped
        if: steps.covector.outputs.commandRan == 'version'
        uses: tauri-apps/create-pull-request@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          title: Apply Version Updates From Current Changes
          commit-message: 'apply version updates'
          labels: 'version updates'
          branch: 'release'
          body: ${{ steps.covector.outputs.change }}
