# Copyright 2022-2022 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT

name: test

on:
  push:
    branches:
      - dev
  pull_request:

env:
  RUST_BACKTRACE: 1

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        platform: ["windows-latest", "macos-latest", "ubuntu-latest"]

    runs-on: ${{ matrix.platform }}

    steps:
      - uses: actions/checkout@v4

      - name: install system deps
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libxdo-dev libayatana-appindicator3-dev

      - uses: dtolnay/rust-toolchain@1.71
      - run: cargo build

      - uses: dtolnay/rust-toolchain@stable
      - run: cargo test
