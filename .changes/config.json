{"gitSiteUrl": "https://www.github.com/tauri-apps/tray-icon", "timeout": 3600000, "pkgManagers": {"rust": {"version": true, "getPublishedVersion": "cargo search ${ pkg.pkg } --limit 1 | sed -nE 's/^[^\"]*\"//; s/\".*//1p' -", "prepublish": ["sudo apt-get update", "sudo apt-get install -y libgtk-3-dev libxdo-dev libayatana-appindicator3-dev"], "publish": [{"command": "cargo package --no-verify", "dryRunCommand": true}, {"command": "echo '<details>\n<summary><em><h4>Cargo Publish</h4></em></summary>\n\n```'", "dryRunCommand": true, "pipe": true}, {"command": "cargo publish", "dryRunCommand": "cargo publish --dry-run", "pipe": true}, {"command": "echo '```\n\n</details>\n'", "dryRunCommand": true, "pipe": true}], "postpublish": ["git tag ${ pkg.pkg }-v${ pkgFile.versionMajor } -f", "git tag ${ pkg.pkg }-v${ pkgFile.versionMajor }.${ pkgFile.versionMinor } -f", "git push --tags -f"]}}, "packages": {"tray-icon": {"path": ".", "manager": "rust", "assets": [{"path": "${ pkg.path }/target/package/tray-icon-${ pkgFile.version }.crate", "name": "${ pkg.pkg }-${ pkgFile.version }.crate"}]}}}